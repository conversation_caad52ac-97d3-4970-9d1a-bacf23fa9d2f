#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ITrace Timeline Visualizer
从itrace.log文件读取指令执行数据，生成HTML时间线可视化网页

Usage:
python itrace_timeline_visualizer.py [itrace.log]
"""

import pandas as pd
import json
import os
import sys
import argparse
from datetime import datetime

class ITraceTimelineVisualizer:
    def __init__(self, filename='itrace.log'):
        """
        初始化ITrace时间线可视化器
        
        Args:
            filename: itrace.log文件路径
        """
        self.filename = filename
        self.data = None
        self.max_time = 0
        self.min_time = 0
        
    def read_itrace_data(self):
        """读取itrace.log数据文件"""
        try:
            if not os.path.exists(self.filename):
                print(f"错误: 文件 {self.filename} 不存在")
                return False

            # 检查文件大小
            file_size = os.path.getsize(self.filename)
            if file_size == 0:
                print(f"错误: 文件 {self.filename} 为空")
                return False

            print(f"正在读取文件: {self.filename} (大小: {file_size} 字节)")

            # 预处理文件，移除注释行并检查格式
            try:
                processed_lines = []
                comment_count = 0
                with open(self.filename, 'r', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        line = line.strip()
                        if not line:
                            continue  # 跳过空行
                        if line.startswith('//'):
                            comment_count += 1
                            continue  # 跳过注释行
                        processed_lines.append(line)

                if not processed_lines:
                    print(f"错误: 文件 {self.filename} 没有有效的数据行（除了注释和空行）")
                    return False

                if comment_count > 0:
                    print(f"跳过了 {comment_count} 行注释")

                print(f"文件头部: {processed_lines[0][:100]}...")

                # 将处理后的内容写入临时文件
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8') as temp_f:
                    temp_f.write('\n'.join(processed_lines))
                    self.temp_filename = temp_f.name

            except UnicodeDecodeError as e:
                print(f"错误: 文件编码问题 - {e}")
                print("提示: 请确保文件使用UTF-8编码")
                return False
            except Exception as e:
                print(f"错误: 无法预处理文件 - {e}")
                return False

            # 读取预处理后的CSV文件
            try:
                df = pd.read_csv(self.temp_filename)
            except pd.errors.EmptyDataError:
                print(f"警告: CSV文件没有数据，但将继续尝试生成HTML")
                df = pd.DataFrame()  # 创建空的DataFrame
            except pd.errors.ParserError as e:
                print(f"警告: CSV解析失败 - {e}")
                print("将尝试使用更宽松的解析方式...")
                try:
                    # 尝试更宽松的解析（使用新的pandas参数）
                    try:
                        df = pd.read_csv(self.temp_filename, on_bad_lines='skip')
                        print(f"使用宽松模式成功读取部分数据")
                    except TypeError:
                        # 如果是旧版本pandas，使用旧参数
                        df = pd.read_csv(self.temp_filename, error_bad_lines=False, warn_bad_lines=True)
                        print(f"使用宽松模式成功读取部分数据")
                except Exception as e2:
                    print(f"宽松模式也失败了: {e2}")
                    print("将创建空的数据集继续处理")
                    df = pd.DataFrame()
            except Exception as e:
                print(f"警告: 读取CSV文件失败 - {e}")
                print("将创建空的数据集继续处理")
                df = pd.DataFrame()

            # 检查是否读取到数据
            if df.empty:
                print(f"警告: CSV文件没有数据行，将生成空的HTML文件")
                # 创建一个包含必要列的空DataFrame
                required_columns = ['InstrID', 'PC', 'Instruction', 'Disassembly',
                                  'FetchStartTime', 'DecodeStartTime', 'DispatchStartTime',
                                  'ExecuteStartTime', 'ExecuteEndTime']
                df = pd.DataFrame(columns=required_columns)
            else:
                print(f"成功读取CSV文件，共 {len(df)} 行数据")

            # 验证必要的列是否存在
            required_columns = ['InstrID', 'PC', 'Instruction', 'Disassembly',
                              'FetchStartTime', 'DecodeStartTime', 'DispatchStartTime',
                              'ExecuteStartTime', 'ExecuteEndTime']

            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"警告: 缺少必要的列: {missing_columns}")
                print(f"文件中的列: {list(df.columns)}")
                print("将为缺少的列添加默认值...")

                # 为缺少的列添加默认值
                for col in missing_columns:
                    if col == 'InstrID':
                        df[col] = range(1, len(df) + 1)  # 自动生成ID
                    elif col in ['PC', 'Instruction', 'Disassembly']:
                        df[col] = 'N/A'  # 字符串列使用N/A
                    else:
                        df[col] = 0  # 时间列使用0
                print("已添加默认值，继续处理...")

            # 检查数据类型和有效性（如果有数据的话）
            if not df.empty:
                print("正在验证数据格式...")
                try:
                    # 检查每一行的数据有效性
                    invalid_rows = []
                    valid_row_count = 0

                    for idx, row in df.iterrows():
                        try:
                            # 检查InstrID是否为数字
                            if pd.isna(row['InstrID']) or not str(row['InstrID']).replace('.', '').replace('-', '').isdigit():
                                invalid_rows.append(f"第{idx+2}行: InstrID '{row['InstrID']}' 不是有效数字")
                                continue

                            # 检查时间列是否为数字
                            time_columns = ['FetchStartTime', 'DecodeStartTime', 'DispatchStartTime',
                                          'ExecuteStartTime', 'ExecuteEndTime']
                            row_valid = True
                            for col in time_columns:
                                if pd.isna(row[col]):
                                    continue  # 允许空值
                                val_str = str(row[col]).replace('.', '').replace('-', '')
                                if not val_str.isdigit():
                                    invalid_rows.append(f"第{idx+2}行: {col} '{row[col]}' 不是有效数字")
                                    row_valid = False
                                    break

                            if row_valid:
                                valid_row_count += 1

                        except Exception as e:
                            invalid_rows.append(f"第{idx+2}行: 数据验证失败 - {e}")

                    # 如果有无效行，显示警告但继续处理
                    if invalid_rows:
                        print(f"⚠️  发现 {len(invalid_rows)} 行数据格式错误:")
                        for error in invalid_rows[:10]:  # 只显示前10个错误
                            print(f"  {error}")
                        if len(invalid_rows) > 10:
                            print(f"  ... 还有 {len(invalid_rows) - 10} 个错误")
                        print(f"有效数据行数: {valid_row_count}")
                        print("将跳过错误行，继续处理有效数据...")
                    else:
                        print(f"所有 {len(df)} 行数据格式正确")

                except Exception as e:
                    print(f"⚠️  数据验证过程中出错 - {e}")
                    print("将跳过验证，继续处理数据...")
            else:
                print("没有数据需要验证")

            # 过滤掉无效的数据行（所有时间都为0的行）
            if not df.empty:
                print("正在过滤有效数据...")
                original_count = len(df)
                try:
                    # 安全地转换时间列为数值类型
                    def safe_to_numeric(series):
                        return pd.to_numeric(series, errors='coerce').fillna(0)

                    fetch_times = safe_to_numeric(df['FetchStartTime'])
                    decode_times = safe_to_numeric(df['DecodeStartTime'])

                    valid_mask = (fetch_times > 0) | (decode_times > 0)
                    df = df[valid_mask].copy()

                    filtered_count = original_count - len(df)
                    if filtered_count > 0:
                        print(f"过滤掉 {filtered_count} 行无效数据（所有时间都为0或无效）")
                except Exception as e:
                    print(f"⚠️  过滤数据时出错: {e}")
                    print("将使用所有数据继续处理...")

            if df.empty:
                print("⚠️  没有找到有效的指令数据，将生成空的HTML文件")
                # 不返回False，而是继续处理空数据

            # 计算时间范围
            print("正在计算时间范围...")
            time_columns = ['FetchStartTime', 'DecodeStartTime', 'DispatchStartTime',
                          'ExecuteStartTime', 'ExecuteEndTime']

            # 找到所有非零时间的最小值和最大值
            all_times = []
            try:
                for col in time_columns:
                    if col in df.columns:
                        # 安全地转换为数值类型
                        numeric_times = pd.to_numeric(df[col], errors='coerce')
                        non_zero_times = numeric_times[numeric_times > 0]
                        if not non_zero_times.empty:
                            all_times.extend(non_zero_times.tolist())

                if all_times:
                    self.min_time = min(all_times)
                    self.max_time = max(all_times)
                else:
                    self.min_time = 0
                    self.max_time = 1000

            except Exception as e:
                print(f"⚠️  计算时间范围时出错: {e}")
                self.min_time = 0
                self.max_time = 1000

            self.data = df
            print(f"✅ 成功加载 {len(df)} 条指令记录")
            print(f"📊 时间范围: {self.min_time} - {self.max_time}")

            # 清理临时文件
            try:
                if hasattr(self, 'temp_filename') and os.path.exists(self.temp_filename):
                    os.unlink(self.temp_filename)
            except Exception as e:
                print(f"⚠️  清理临时文件时出错: {e}")

            return True

        except FileNotFoundError:
            print(f"错误: 文件 {self.filename} 不存在")
            print("提示: 请确保文件路径正确")
            return False
        except PermissionError:
            print(f"错误: 没有权限读取文件 {self.filename}")
            print("提示: 请检查文件权限")
            return False
        except MemoryError:
            print(f"错误: 内存不足，无法读取文件 {self.filename}")
            print("提示: 文件可能过大，请尝试分割文件或增加内存")
            return False
        except Exception as e:
            print(f"❌ 读取文件时出现未知错误: {e}")
            print(f"错误类型: {type(e).__name__}")
            import traceback
            print("详细错误信息:")
            traceback.print_exc()
            return False
        finally:
            # 确保清理临时文件
            try:
                if hasattr(self, 'temp_filename') and os.path.exists(self.temp_filename):
                    os.unlink(self.temp_filename)
            except:
                pass
    
    def process_instruction_data(self):
        """处理指令数据，计算各阶段的持续时间"""
        if self.data is None:
            print("错误: 没有数据可以处理")
            return []

        instructions = []
        processing_errors = []

        print(f"正在处理 {len(self.data)} 条指令数据...")

        for idx, row in self.data.iterrows():
            try:
                # 验证必要字段
                if pd.isna(row['InstrID']):
                    processing_errors.append(f"第{idx+2}行: InstrID为空")
                    continue

                if pd.isna(row['PC']):
                    processing_errors.append(f"第{idx+2}行: PC为空")
                    continue

                # 尝试转换InstrID为整数
                try:
                    instr_id = int(float(row['InstrID']))  # 先转float再转int，处理可能的小数
                except (ValueError, TypeError) as e:
                    processing_errors.append(f"第{idx+2}行: InstrID '{row['InstrID']}' 无法转换为整数 - {e}")
                    continue

                instr = {
                    'id': instr_id,
                    'pc': str(row['PC']) if not pd.isna(row['PC']) else 'N/A',
                    'instruction': str(row['Instruction']) if not pd.isna(row['Instruction']) else 'N/A',
                    'disassembly': str(row['Disassembly']) if not pd.isna(row['Disassembly']) else 'N/A',
                    'stages': []
                }

                # 处理各个执行阶段
                stages_info = [
                    ('fetch', 'FetchStartTime', 'DecodeStartTime', '#FF6B6B'),
                    ('decode', 'DecodeStartTime', 'DispatchStartTime', '#4ECDC4'),
                    ('dispatch', 'DispatchStartTime', 'ExecuteStartTime', '#45B7D1'),
                    ('execute', 'ExecuteStartTime', 'ExecuteEndTime', '#96CEB4')
                ]

                for stage_name, start_col, end_col, color in stages_info:
                    try:
                        start_time = row[start_col]
                        end_time = row[end_col]

                        # 转换为数值类型并检查开始时间是否有效
                        try:
                            start_time = float(start_time)
                            if not pd.isna(end_time):
                                end_time = float(end_time)
                            else:
                                end_time = 0
                        except (ValueError, TypeError) as e:
                            processing_errors.append(f"第{idx+2}行: {stage_name}阶段时间转换失败 - {e}")
                            continue

                        # 检查开始时间是否有效（现在start_time已经是数值类型）
                        if start_time <= 0:
                            continue

                        # 如果end_time为0或小于start_time，使用start_time作为end_time
                        if end_time <= 0 or end_time < start_time:
                            if stage_name == 'fetch':
                                # fetch阶段如果没有结束时间，假设持续1个时间单位
                                end_time = start_time + 1
                            else:
                                # 其他阶段如果没有结束时间，跳过
                                continue

                        stage = {
                            'name': stage_name,
                            'start': int(start_time),
                            'end': int(end_time),
                            'duration': int(end_time - start_time),
                            'color': color
                        }
                        instr['stages'].append(stage)

                    except Exception as e:
                        processing_errors.append(f"第{idx+2}行: 处理{stage_name}阶段时出错 - {e}")
                        continue

                # 计算总执行时间
                if instr['stages']:
                    try:
                        instr['total_start'] = min(stage['start'] for stage in instr['stages'])
                        instr['total_end'] = max(stage['end'] for stage in instr['stages'])
                        instr['total_duration'] = instr['total_end'] - instr['total_start']
                    except Exception as e:
                        processing_errors.append(f"第{idx+2}行: 计算总执行时间时出错 - {e}")
                        instr['total_start'] = 0
                        instr['total_end'] = 0
                        instr['total_duration'] = 0
                else:
                    instr['total_start'] = 0
                    instr['total_end'] = 0
                    instr['total_duration'] = 0

                instructions.append(instr)

            except Exception as e:
                processing_errors.append(f"第{idx+2}行: 处理指令数据时出现未知错误 - {e}")
                continue

        # 显示处理错误信息
        if processing_errors:
            print(f"⚠️  处理过程中发现 {len(processing_errors)} 个错误:")
            for error in processing_errors[:10]:  # 只显示前10个错误
                print(f"  {error}")
            if len(processing_errors) > 10:
                print(f"  ... 还有 {len(processing_errors) - 10} 个错误")
            print("已跳过错误数据，继续处理...")

        if not instructions:
            print("⚠️  没有成功处理任何指令数据，将生成空的HTML文件")
            # 不返回空列表，而是返回一个空的指令列表，这样HTML仍然可以生成

        # 按照指令ID排序
        try:
            instructions.sort(key=lambda x: x['id'])
            print(f"✅ 成功处理 {len(instructions)} 条指令数据")
        except Exception as e:
            print(f"⚠️  排序时出错: {e}")
            print("继续使用未排序的数据")

        return instructions

    def generate_html_timeline(self, instructions, output_filename='itrace_timeline.html'):
        """生成HTML时间线可视化"""

        # 计算时间轴的缩放比例
        time_range = self.max_time - self.min_time
        if time_range == 0:
            time_range = 1000

        # HTML模板
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ITrace 指令执行时间线</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}

        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}

        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            text-align: center;
        }}

        .stats {{
            display: flex;
            justify-content: space-around;
            margin-top: 15px;
            font-size: 1.1em;
        }}

        .timeline-container {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            width: 100%;
            max-width: 100%;
            display: flex;
            flex-direction: column;
        }}

        .timeline-content {{
            display: flex;
            position: relative;
            width: 100%;
        }}

        .fixed-left-panel {{
            width: 350px;
            flex-shrink: 0;
            background: #fafafa;
            border-right: 2px solid #e0e0e0;
            z-index: 20;
            position: relative;
        }}

        .scrollable-right-panel {{
            flex: 1;
            overflow-x: auto;
            overflow-y: hidden;
            position: relative;
        }}

        #instructionList {{
            position: relative;
        }}

        .timeline-header {{
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }}

        .legend {{
            display: flex;
            gap: 20px;
            margin-left: auto;
        }}

        .legend-item {{
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }}

        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }}

        .time-axis {{
            position: relative;
            height: 40px;
            margin-bottom: 20px;
            border-bottom: 2px solid #ddd;
            min-width: 800px;
            overflow-x: auto;
            margin-left: 350px;
            margin-right: 0px;
        }}

        .time-tick {{
            position: absolute;
            bottom: 0;
            width: 1px;
            height: 10px;
            background: #666;
        }}

        .time-label {{
            position: absolute;
            bottom: -25px;
            font-size: 12px;
            color: #666;
            transform: translateX(-50%);
        }}

        .instruction-row {{
            display: flex;
            align-items: stretch;
            margin-bottom: 8px;
            height: 45px;
            position: relative;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }}

        .instruction-left {{
            width: 350px;
            padding: 8px;
            background: #fafafa;
            border-radius: 8px 0 0 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 45px;
            box-sizing: border-box;
        }}

        .instruction-right {{
            flex: 1;
            background: #fafafa;
            border-radius: 0 8px 8px 0;
            position: relative;
            min-width: 800px;
            height: 45px;
            box-sizing: border-box;
        }}

        .instruction-row:hover {{
            background: #e3f2fd;
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }}

        .instruction-info {{
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }}

        .instruction-pc {{
            color: #666;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            font-weight: bold;
            margin-bottom: 3px;
        }}

        .instruction-disasm {{
            color: #2c3e50;
            font-family: 'Courier New', monospace;
            font-size: 1.0em;
            font-weight: bold;
            word-break: break-all;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 4px 8px;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }}

        .timeline-bars {{
            position: relative;
            height: 29px;
            width: 100%;
            margin: 8px;
            overflow: visible;
            display: flex;
            align-items: center;
        }}

        .stage-bar {{
            position: absolute;
            height: 20px;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.7);
            cursor: pointer;
            transition: all 0.2s ease;
            top: 4px;
            border: 1px solid rgba(0,0,0,0.2);
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }}

        .stage-bar:hover {{
            transform: scale(1.05);
            z-index: 100 !important;
            box-shadow: 0 3px 6px rgba(0,0,0,0.4);
            border: 2px solid rgba(255,255,255,0.8);
        }}

        /* 所有阶段都在同一高度 */
        .stage-bar.fetch {{
            z-index: 4;
            top: 4px;
        }}
        .stage-bar.decode {{
            z-index: 3;
            top: 4px;
        }}
        .stage-bar.dispatch {{
            z-index: 2;
            top: 4px;
        }}
        .stage-bar.execute {{
            z-index: 1;
            top: 4px;
        }}

        .tooltip {{
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.2s;
        }}

        .controls {{
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }}

        .control-group {{
            display: flex;
            align-items: center;
            gap: 8px;
        }}

        .control-group label {{
            font-weight: 500;
            color: #555;
        }}

        .control-group input, .control-group select {{
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }}

        .btn {{
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }}

        .btn:hover {{
            background: #0056b3;
        }}

        .summary {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}

        .summary h3 {{
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }}

        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }}

        .summary-item {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }}

        .summary-value {{
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }}

        .summary-label {{
            color: #666;
            margin-top: 5px;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>ITrace 指令执行时间线可视化</h1>
        <div class="stats">
            <div>总指令数: <strong>{total_instructions}</strong></div>
            <div>时间范围: <strong>{min_time} - {max_time}</strong></div>
            <div>总执行时间: <strong>{total_time}</strong></div>
            <div>生成时间: <strong>{generation_time}</strong></div>
        </div>
    </div>

    <div class="timeline-container">
        <div class="timeline-header">
            <h3 style="margin: 0;">指令执行时间线</h3>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #FF6B6B;"></div>
                    <span>Fetch</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4ECDC4;"></div>
                    <span>Decode</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #45B7D1;"></div>
                    <span>Dispatch</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #96CEB4;"></div>
                    <span>Execute</span>
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>缩放:</label>
                <input type="range" id="zoomSlider" min="0.1" max="20" step="0.1" value="1">
                <span id="zoomValue">1.0x</span>
            </div>
            <div class="control-group">
                <label>过滤指令:</label>
                <input type="text" id="filterInput" placeholder="输入PC地址或反汇编代码...">
            </div>
            <div class="control-group">
                <label>排序:</label>
                <select id="sortSelect">
                    <option value="id">指令ID</option>
                    <option value="start_time">开始时间</option>
                    <option value="duration">执行时长</option>
                </select>
            </div>
            <button class="btn" onclick="resetView()">重置视图</button>
            <div class="control-group">
                <label>快捷键:</label>
                <span style="font-size: 12px; color: #666;">W/S: 放大/缩小 | A/D: 左移/右移 | R: 复位缩放</span>
            </div>
        </div>

        <div class="time-axis" id="timeAxis"></div>

        <div class="timeline-content">
            <div class="fixed-left-panel">
                <div id="leftPanelContent"></div>
            </div>
            <div class="scrollable-right-panel">
                <div id="instructionList"></div>
            </div>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <div class="summary">
        <h3>执行统计摘要</h3>
        <div class="summary-grid">
            {summary_stats}
        </div>
    </div>

    <script>
        const instructions = {instructions_json};
        const minTime = {min_time};
        const maxTime = {max_time};
        const timeRange = maxTime - minTime;

        let currentZoom = 1.0;
        let currentFilter = '';
        let currentSort = 'id';
        let currentScrollLeft = 0;

        // 初始化时间轴
        function initTimeAxis() {{
            const timeAxis = document.getElementById('timeAxis');
            timeAxis.innerHTML = ''; // 清空现有内容
            const tickCount = 10;

            for (let i = 0; i <= tickCount; i++) {{
                const time = minTime + (timeRange * i / tickCount);
                const position = (i / tickCount) * 100 * currentZoom;

                const tick = document.createElement('div');
                tick.className = 'time-tick';
                tick.style.left = position + '%';
                timeAxis.appendChild(tick);

                const label = document.createElement('div');
                label.className = 'time-label';
                label.style.left = position + '%';
                label.textContent = Math.round(time);
                timeAxis.appendChild(label);
            }}
        }}

        // 计算时间条的位置和宽度
        function calculateBarPosition(start, end) {{
            const startPercent = ((start - minTime) / timeRange) * 100 * currentZoom;
            const widthPercent = ((end - start) / timeRange) * 100 * currentZoom;
            return {{ left: startPercent, width: Math.max(widthPercent, 0.5) }};
        }}

        // 显示工具提示
        function showTooltip(event, stage, instruction) {{
            const tooltip = document.getElementById('tooltip');
            tooltip.innerHTML = `
                <strong>${{stage.name.toUpperCase()}}</strong><br>
                指令: ${{instruction.disassembly}}<br>
                开始: ${{stage.start}}<br>
                结束: ${{stage.end}}<br>
                持续: ${{stage.duration}}
            `;
            tooltip.style.left = event.pageX + 10 + 'px';
            tooltip.style.top = event.pageY - 10 + 'px';
            tooltip.style.opacity = '1';
        }}

        // 隐藏工具提示
        function hideTooltip() {{
            document.getElementById('tooltip').style.opacity = '0';
        }}

        // 渲染指令列表
        function renderInstructions(filteredInstructions = null) {{
            const instructionList = document.getElementById('instructionList');
            const leftPanelContent = document.getElementById('leftPanelContent');
            const instructionsToRender = filteredInstructions || instructions;

            instructionList.innerHTML = '';
            leftPanelContent.innerHTML = '';

            instructionsToRender.forEach(instruction => {{
                // 创建左侧固定信息
                const leftInfo = document.createElement('div');
                leftInfo.className = 'instruction-left';
                leftInfo.innerHTML = `
                    <div class="instruction-pc">${{instruction.pc}}</div>
                    <div class="instruction-disasm">${{instruction.disassembly}}</div>
                `;
                leftPanelContent.appendChild(leftInfo);

                // 创建右侧时间线
                const rightPanel = document.createElement('div');
                rightPanel.className = 'instruction-right';

                const timeline = document.createElement('div');
                timeline.className = 'timeline-bars';
                timeline.style.width = (800 * currentZoom) + 'px';

                instruction.stages.forEach(stage => {{
                    const bar = document.createElement('div');
                    bar.className = `stage-bar ${{stage.name}}`;
                    bar.style.backgroundColor = stage.color;

                    const position = calculateBarPosition(stage.start, stage.end);
                    bar.style.left = position.left + '%';
                    bar.style.width = position.width + '%';

                    if (position.width > 2) {{
                        bar.textContent = stage.name;
                    }}

                    bar.addEventListener('mouseenter', (e) => showTooltip(e, stage, instruction));
                    bar.addEventListener('mouseleave', hideTooltip);

                    timeline.appendChild(bar);
                }});

                rightPanel.appendChild(timeline);
                instructionList.appendChild(rightPanel);
            }});
        }}

        // 过滤指令
        function filterInstructions() {{
            const filter = document.getElementById('filterInput').value.toLowerCase();
            if (!filter) {{
                renderInstructions();
                return;
            }}

            const filtered = instructions.filter(instr =>
                instr.pc.toLowerCase().includes(filter) ||
                instr.disassembly.toLowerCase().includes(filter)
            );

            renderInstructions(filtered);
        }}

        // 排序指令
        function sortInstructions() {{
            const sortBy = document.getElementById('sortSelect').value;
            const sorted = [...instructions];

            sorted.sort((a, b) => {{
                switch(sortBy) {{
                    case 'start_time':
                        return a.total_start - b.total_start;
                    case 'duration':
                        return b.total_duration - a.total_duration;
                    default:
                        return a.id - b.id;
                }}
            }});

            renderInstructions(sorted);
        }}

        // 缩放功能
        function updateZoom() {{
            const zoom = parseFloat(document.getElementById('zoomSlider').value);
            currentZoom = zoom;
            document.getElementById('zoomValue').textContent = zoom.toFixed(1) + 'x';

            // 更新时间轴的宽度
            const baseWidth = 800;
            const newWidth = (baseWidth * zoom) + 'px';

            const timeAxis = document.getElementById('timeAxis');
            timeAxis.style.minWidth = newWidth;

            // 更新右侧面板的宽度以适应缩放
            const scrollablePanel = document.querySelector('.scrollable-right-panel');
            if (scrollablePanel) {{
                scrollablePanel.style.width = newWidth;
            }}

            // 重新渲染时间轴和指令以应用缩放
            initTimeAxis();
            renderInstructions();
        }}

        // 重置视图
        function resetView() {{
            document.getElementById('zoomSlider').value = 1;
            document.getElementById('filterInput').value = '';
            document.getElementById('sortSelect').value = 'id';
            currentScrollLeft = 0;
            updateZoom();
            renderInstructions();
            // 重置滚动位置
            const timelineContainer = document.querySelector('.timeline-container');
            timelineContainer.scrollLeft = 0;
        }}

        // 键盘快捷键处理
        function handleKeyPress(event) {{
            // 如果用户正在输入框中输入，不处理快捷键
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'SELECT') {{
                return;
            }}

            const zoomSlider = document.getElementById('zoomSlider');
            const timelineContainer = document.querySelector('.timeline-container');
            const scrollStep = 50; // 滚动步长
            const zoomStep = 0.2; // 缩放步长

            switch(event.key.toLowerCase()) {{
                case 'w': // 放大
                    event.preventDefault();
                    const newZoomUp = Math.min(parseFloat(zoomSlider.value) + zoomStep, 20);
                    zoomSlider.value = newZoomUp;
                    updateZoom();
                    break;

                case 's': // 缩小
                    event.preventDefault();
                    const newZoomDown = Math.max(parseFloat(zoomSlider.value) - zoomStep, 0.1);
                    zoomSlider.value = newZoomDown;
                    updateZoom();
                    break;

                case 'a': // 左移
                    event.preventDefault();
                    timelineContainer.scrollLeft = Math.max(timelineContainer.scrollLeft - scrollStep, 0);
                    currentScrollLeft = timelineContainer.scrollLeft;
                    break;

                case 'd': // 右移
                    event.preventDefault();
                    timelineContainer.scrollLeft = Math.min(
                        timelineContainer.scrollLeft + scrollStep,
                        timelineContainer.scrollWidth - timelineContainer.clientWidth
                    );
                    currentScrollLeft = timelineContainer.scrollLeft;
                    break;

                case 'r': // 复位缩放
                    event.preventDefault();
                    zoomSlider.value = 1;
                    updateZoom();
                    timelineContainer.scrollLeft = 0;
                    currentScrollLeft = 0;
                    break;
            }}
        }}

        // 事件监听器
        document.getElementById('zoomSlider').addEventListener('input', updateZoom);
        document.getElementById('filterInput').addEventListener('input', filterInstructions);
        document.getElementById('sortSelect').addEventListener('change', sortInstructions);

        // 键盘事件监听器
        document.addEventListener('keydown', handleKeyPress);

        // 初始化
        initTimeAxis();
        renderInstructions();
    </script>
</body>
</html>
        """

        return html_template

    def generate_instruction_rows(self, instructions):
        """生成指令行的HTML"""
        left_rows = []
        right_rows = []

        for instr in instructions:
            # 生成左侧固定信息
            left_html = f"""
                <div class="instruction-left">
                    <div class="instruction-info">
                        <div class="instruction-pc">{instr['pc']}</div>
                        <div class="instruction-disasm">{instr['disassembly']}</div>
                    </div>
                </div>
            """
            left_rows.append(left_html)

            # 生成右侧时间线
            if instr['stages']:
                stage_bars = []
                for stage in instr['stages']:
                    start_percent = ((stage['start'] - self.min_time) / (self.max_time - self.min_time)) * 100
                    width_percent = ((stage['end'] - stage['start']) / (self.max_time - self.min_time)) * 100
                    width_percent = max(width_percent, 0.5)  # 最小宽度

                    stage_bars.append(f"""
                        <div class="stage-bar {stage['name']}"
                             style="left: {start_percent:.2f}%; width: {width_percent:.2f}%; background-color: {stage['color']};"
                             onmouseenter="showTooltip(event, {json.dumps(stage)}, {json.dumps(instr)})"
                             onmouseleave="hideTooltip()">
                            {stage['name'] if width_percent > 2 else ''}
                        </div>
                    """)

                right_html = f"""
                    <div class="instruction-right">
                        <div class="timeline-bars">
                            {''.join(stage_bars)}
                        </div>
                    </div>
                """
            else:
                right_html = f"""
                    <div class="instruction-right">
                        <div class="timeline-bars"></div>
                    </div>
                """
            right_rows.append(right_html)

        # 返回一个包含左右两部分的字典
        return {
            'left_content': '\n'.join(left_rows),
            'right_content': '\n'.join(right_rows)
        }

    def generate_summary_stats(self, instructions):
        """生成统计摘要"""
        if not instructions:
            return ""

        # 计算各种统计数据
        total_instructions = len(instructions)

        # 各阶段平均持续时间
        stage_durations = {'fetch': [], 'decode': [], 'dispatch': [], 'execute': []}
        total_durations = []

        for instr in instructions:
            if instr['total_duration'] > 0:
                total_durations.append(instr['total_duration'])

            for stage in instr['stages']:
                if stage['name'] in stage_durations:
                    stage_durations[stage['name']].append(stage['duration'])

        # 计算平均值
        avg_total = sum(total_durations) / len(total_durations) if total_durations else 0
        avg_fetch = sum(stage_durations['fetch']) / len(stage_durations['fetch']) if stage_durations['fetch'] else 0
        avg_decode = sum(stage_durations['decode']) / len(stage_durations['decode']) if stage_durations['decode'] else 0
        avg_dispatch = sum(stage_durations['dispatch']) / len(stage_durations['dispatch']) if stage_durations['dispatch'] else 0
        avg_execute = sum(stage_durations['execute']) / len(stage_durations['execute']) if stage_durations['execute'] else 0

        # 最长和最短执行时间
        max_duration = max(total_durations) if total_durations else 0
        min_duration = min(total_durations) if total_durations else 0

        # 生成HTML
        stats_html = f"""
            <div class="summary-item">
                <div class="summary-value">{total_instructions}</div>
                <div class="summary-label">总指令数</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">{avg_total:.1f}</div>
                <div class="summary-label">平均执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">{max_duration}</div>
                <div class="summary-label">最长执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">{min_duration}</div>
                <div class="summary-label">最短执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">{avg_fetch:.1f}</div>
                <div class="summary-label">平均Fetch时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">{avg_decode:.1f}</div>
                <div class="summary-label">平均Decode时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">{avg_dispatch:.1f}</div>
                <div class="summary-label">平均Dispatch时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">{avg_execute:.1f}</div>
                <div class="summary-label">平均Execute时间</div>
            </div>
        """

        return stats_html

    def create_visualization(self, output_filename='itrace_timeline.html'):
        """创建完整的可视化HTML文件"""
        print("开始创建可视化...")

        # 读取数据
        if not self.read_itrace_data():
            print("❌ 数据读取失败，无法继续")
            return False

        # 处理指令数据
        instructions = self.process_instruction_data()
        if not instructions:
            print("⚠️  没有有效的指令数据，将生成空的HTML文件")
            # 不返回False，继续生成空的HTML文件

        print(f"📝 正在生成HTML可视化文件: {output_filename}")

        # 生成各部分内容
        try:
            print("正在生成统计摘要...")
            summary_stats = self.generate_summary_stats(instructions)

            print("正在生成HTML模板...")
            html_template = self.generate_html_timeline(instructions, output_filename)

        except Exception as e:
            print(f"❌ 生成HTML内容时出错: {e}")
            import traceback
            print("详细错误信息:")
            traceback.print_exc()
            return False

        # 填充HTML模板
        try:
            print("正在填充HTML模板...")
            html_content = html_template.format(
                total_instructions=len(instructions),
                min_time=int(self.min_time),
                max_time=int(self.max_time),
                total_time=int(self.max_time - self.min_time),
                generation_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                instructions_json=json.dumps(instructions, ensure_ascii=False),
                summary_stats=summary_stats
            )
        except Exception as e:
            print(f"❌ 填充HTML模板时出错: {e}")
            print("可能的原因:")
            print("  - 模板格式错误")
            print("  - 数据包含无法序列化的内容")
            print("  - 内存不足")
            import traceback
            print("详细错误信息:")
            traceback.print_exc()
            return False

        # 检查输出目录是否存在
        output_dir = os.path.dirname(os.path.abspath(output_filename))
        if output_dir and not os.path.exists(output_dir):
            try:
                print(f"创建输出目录: {output_dir}")
                os.makedirs(output_dir, exist_ok=True)
            except Exception as e:
                print(f"❌ 无法创建输出目录 {output_dir}: {e}")
                return False

        # 写入文件
        try:
            print(f"正在写入文件: {output_filename}")

            # 检查磁盘空间（估算）
            html_size = len(html_content.encode('utf-8'))
            print(f"HTML文件大小: {html_size / 1024 / 1024:.2f} MB")

            with open(output_filename, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # 验证文件是否成功写入
            if not os.path.exists(output_filename):
                print(f"❌ 文件写入失败: {output_filename} 不存在")
                return False

            actual_size = os.path.getsize(output_filename)
            if actual_size == 0:
                print(f"❌ 文件写入失败: {output_filename} 为空")
                return False

            print(f"✅ HTML可视化文件已生成: {output_filename}")
            print(f"📊 包含 {len(instructions)} 条指令的时间线")
            print(f"⏱️  时间范围: {self.min_time} - {self.max_time}")
            print(f"📁 文件大小: {actual_size / 1024 / 1024:.2f} MB")
            print(f"📍 文件路径: {os.path.abspath(output_filename)}")
            return True

        except PermissionError:
            print(f"❌ 权限错误: 无法写入文件 {output_filename}")
            print("提示: 请检查文件权限或选择其他输出路径")
            return False
        except OSError as e:
            print(f"❌ 系统错误: 无法写入文件 {output_filename} - {e}")
            print("可能的原因:")
            print("  - 磁盘空间不足")
            print("  - 文件路径无效")
            print("  - 文件被其他程序占用")
            return False
        except UnicodeEncodeError as e:
            print(f"❌ 编码错误: 无法写入文件 {output_filename} - {e}")
            print("提示: 数据中包含无法编码的字符")
            return False
        except Exception as e:
            print(f"❌ 写入HTML文件时出现未知错误: {e}")
            print(f"错误类型: {type(e).__name__}")
            import traceback
            print("详细错误信息:")
            traceback.print_exc()
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ITrace Timeline Visualizer - 生成指令执行时间线HTML可视化')
    parser.add_argument('input_file', nargs='?', default='itrace.log',
                       help='输入的itrace.log文件路径 (默认: itrace.log)')
    parser.add_argument('-o', '--output', default='itrace_timeline.html',
                       help='输出的HTML文件路径 (默认: itrace_timeline.html)')
    parser.add_argument('--open', action='store_true',
                       help='生成后自动在浏览器中打开HTML文件')

    args = parser.parse_args()

    print("=" * 60)
    print("ITrace Timeline Visualizer")
    print("=" * 60)
    print(f"输入文件: {args.input_file}")
    print(f"输出文件: {args.output}")
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)

    # 检查输入文件是否存在
    if not os.path.exists(args.input_file):
        print(f"错误: 输入文件 '{args.input_file}' 不存在")
        print("请确保itrace.log文件已经生成")
        sys.exit(1)

    # 创建可视化器
    visualizer = ITraceTimelineVisualizer(args.input_file)

    # 生成可视化
    success = visualizer.create_visualization(args.output)

    if success:
        print("-" * 60)
        print("✅ 可视化生成成功!")
        print(f"📁 输出文件: {os.path.abspath(args.output)}")

        # 如果指定了--open参数，尝试在浏览器中打开
        if args.open:
            try:
                import webbrowser
                webbrowser.open(f'file://{os.path.abspath(args.output)}')
                print("🌐 已在浏览器中打开HTML文件")
            except Exception as e:
                print(f"⚠️  无法自动打开浏览器: {e}")
                print("请手动在浏览器中打开HTML文件")
        else:
            print("💡 使用 --open 参数可以自动在浏览器中打开文件")
    else:
        print("❌ 可视化生成失败")
        sys.exit(1)

def create_test_data():
    """创建测试数据用于验证功能"""
    test_data = """InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x000000001000,0x00000000fb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020
2,0x000000001008,0x00000000fb234567,"tmma.ttt t3, t1, t2",1025,1030,1035,1040,1055
3,0x000000001010,0x00000000fb0012ab,"twait",1050,1055,0,0,0
4,0x000000001014,0x00000000fb801000,"ace_bsync x0",1060,1065,0,0,0
5,0x000000001018,0x00000000fb345678,"tcsrw.i 0x5",1070,1075,1080,1085,1090
6,0x000000001020,0x00000000fb456789,"tld.linear.u16 t6, (x11)",1095,1100,1105,1110,1125
7,0x000000001028,0x00000000fb567890,"tmma.tnt t4, t2, t3",1130,1135,1140,1145,1165
8,0x000000001030,0x00000000fb678901,"tst.linear.u32 (x12), t7",1170,1175,1180,1185,1195
"""

    with open('test_itrace.log', 'w', encoding='utf-8') as f:
        f.write(test_data)

    print("测试数据已生成: test_itrace.log")
    return 'test_itrace.log'

if __name__ == "__main__":
    # 如果没有提供参数且itrace.log不存在，创建测试数据
    if len(sys.argv) == 1 and not os.path.exists('itrace.log'):
        print("未找到itrace.log文件，正在创建测试数据...")
        test_file = create_test_data()
        print(f"使用测试数据: {test_file}")
        sys.argv.append(test_file)

    main()
